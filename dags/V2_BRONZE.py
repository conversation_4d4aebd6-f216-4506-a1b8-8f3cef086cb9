from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

instances = [
    Bronze('dealer').add_tables([
        'atendimento',
        'bqvw_DwErpPesados_FaturamentoVeiculos',
        'comissaoempresa',
        'comissao',
        'comissaonatureza',
        'comissaoperfil',
        'comissaoperfilregra',
        'comissaoperfilregrafaixa',
        'comissaosetorservico',
        'comissaotipoproduto',
        'cor',
        'combustivel',
        'DepartamentoEmpresa',
        'empresaestoque',
        'empresatabelapreco',
        'enquadramento',
        'estoqueempresa',
        'estoquenatoperacao',
        'grupoempresa',
        'FichaRazao',
        'FichaRazaoMov',
        'TipoFichaRazao',
        'inventarioproduto',
        'motvendaperdida',
        'movimentoestoque',
        'movimentoestoquehistorico',
        'movimentoestoqueitem',
        'movimentoestoqueitemmoeda',
        'naturezaoperacaotipoitem',
        'notafiscal',
        'notafiscalitemoficinaproduto',
        'notafiscalitemoficinaservico',
        'notafiscalitemtributo',
        'notafiscalnfreferencia',
        'ntihistorico',
        'oficinamarcacao',
        'oficinaservico',
        'oficinaservicorateio',
        'os',
        'oshistorico',
        'ostipoos',
        'ostipooshistorico',
        'PedidoCompra',
        'pessoa',
        'pessoaendereco',
        'pessoaenquadramento',
        'pessoatelefone',
        'proposta',
        'propostahistorico',
        'propostaparcela',
        'produtoestoque',
        'produtoestoquemoeda',
        'produtopedido',
        'produtopedidoitem',
        'produtoreferencia',
        'PlanoConta',
        'ramoatividade',
        'rel_Veiculos',
        'RequisicaoCompra',
        'RequisicaoCompraItem',
        'RequisicaoCompraItemPedido',
        'SaldoContabil',
        'segmentomercado',
        'tabelapreco',
        'tipopagproposta',
        'tipoosemp',
        'tipotitulo',
        'tributo',
        'usuarioperfilacesso',
        'veiculobloqueio',
        'veiculoestoque',
        'veiculomovimento',
        'veiculoprecousado',
        'veiculotransflocal',
        'vendaperdida',
        'VeiculoValorAgregado',
        'ValorAgregado',
        'vendaperdidahistorico',
        {
        "notafiscalitem": {
            "times_per_day": 2
        },
        "notafiscalitemcusto": {
            "times_per_day": 2
        },
        "titulo": {
            "times_per_day": 1
        },
        "agentecobrador": {
                "id_columns": ["agentecobrador_codigo"],
                "date_columns": ["agentecobrador_dataCriacao", "agentecobrador_dataAlteracao"]
        },
        "condicaopagamento": {
                "id_columns": ["condicaopagamento_codigo"],
                "date_columns": ["condicaopagamento_dataCriacao", "condicaopagamento_dataAlteracao"]
        },
        "custo": {
                "id_columns": ["custo_codigo"],
                "date_columns": ["custo_dataCriacao", "custo_dataAlteracao"]
        },
        "departamento": {
                "id_columns": ["departamento_codigo"],
                "date_columns": ["departamento_dataCriacao", "departamento_dataAlteracao"]
        },
        "empresa": {
                "id_columns": ["empresa_codigo"],
                "date_columns": ["empresa_dataCriacao", "empresa_dataAlteracao"]
        },
        "estado": {
                "id_columns": ["estado_codigo"],
                "date_columns": ["estado_dataCriacao", "estado_dataAlteracao"]
        },
        "estoque": {
                "id_columns": ["estoque_codigo"],
                "date_columns": ["estoque_dataCriacao", "estoque_dataAlteracao"]
        },
        "familiaveiculo": {
                "id_columns": ["familiaveiculo_codigo"],
                "date_columns": ["familiaveiculo_dataCriacao", "familiaveiculo_dataAlteracao"]
        },
        "grupoproduto": {
                "id_columns": ["grupoproduto_codigo"],
                "date_columns": ["grupoproduto_dataCriacao", "grupoproduto_dataAlteracao"]
        },
        "grupolucratividade": {
                "id_columns": ["grupolucratividade_codigo"],
                "date_columns": ["grupolucratividade_dataCriacao", "grupolucratividade_dataAlteracao"]
        },
        'lancamento' : {
                "times_per_day": 1
        },
        "localveiculo": {
                "id_columns": ["localveiculo_codigo"],
                "date_columns": ["localveiculo_dataCriacao", "localveiculo_dataAlteracao"]
        },
        "marca": {
                "id_columns": ["marca_codigo"],
                "date_columns": ["marca_dataCriacao", "marca_dataAlteracao"]
        },
        "modeloveiculo": {
                "id_columns": ["modeloveiculo_codigo"],
                "date_columns": ["modeloveiculo_dataCriacao", "modeloveiculo_dataAlteracao"]
        },
        "municipio": {
                "id_columns": ["municipio_codigo"],
                "date_columns": ["municipio_dataCriacao", "municipio_dataAlteracao"]
        },
        "naturezaoperacao": {
                "id_columns": ["naturezaoperacao_codigo"],
                "date_columns": ["naturezaoperacao_dataCriacao", "naturezaoperacao_dataAlteracao"]
        },
        "nti": {
                "id_columns": ["nti_codigo"],
                #"date_columns": [""]
        },
        "ntiitem": {
                "id_columns": ["nti_codigo", "ntiItem_codigo"],
                #"date_columns": [""]
        },
        "oficinaproduto": {
                "id_columns": ["oficinaproduto_codigo"],
                "date_columns": []
        },
        "oficinarequisicao": {
                "id_columns": ["oficinarequisicao_codigo"],
                "date_columns": ["oficinarequisicao_data"]
        },
        "oficinarequisicaoitem": {
                "id_columns": ["oficinarequisicao_codigo", "oficinarequisicaoitem_codigo"],
                "date_columns": []
        },
        "produto": {
                "id_columns": ["produto_codigo"]
        },
        "produtomarca": {
                "id_columns": ["produto_codigo", "produtomarca_marcacod"]
        },
        "tipodocumento": {
                "id_columns": ["tipodocumento_codigo"],
                "date_columns": ["tipodocumento_dataCriacao", "tipodocumento_dataAlteracao"]
        },
        "tipoendereco": {
                "id_columns": ["tipoendereco_codigo"],
                "date_columns": ["tipoendereco_dataCriacao", "tipoendereco_dataAlteracao"]
        },
        "tipoproduto": {
                "id_columns": ["tipoproduto_codigo"],
                "date_columns": ["tipoproduto_dataCriacao", "tipoproduto_dataAlteracao"]
        },
        "tipoos": {
                "id_columns": ["tipoos_codigo"],
                "date_columns": ["tipoos_dataCriacao", "tipoos_dataAlteracao"]
        },
        "tiposervico": {
                "id_columns": ["tiposervico_codigo"],
                "date_columns": ["tiposervico_dataCriacao", "tiposervico_dataAlteracao"]
        },
        "tmo": {
                "id_columns": ["tmo_codigo"],
                "date_columns": ["tmo_dataCriacao", "tmo_dataAlteracao"]
        },
        "usuario": {
                "id_columns": ["usuario_codigo"],
                "date_columns": ["usuario_dataCriacao", "usuario_dataAlteracao"]
        },
        "veiculo": {
                "id_columns": ["veiculo_codigo"],
                "date_columns": ["veiculo_dataCriacao", "veiculo_dataAlteracao"]
        },
        "veiculoano": {
            "id_columns": ["veiculoano_codigo"],
            "date_columns": ["veiculoano_dataCriacao", "veiculoano_dataAlteracao"]
        },
        "vendasveiculos": {
                "normal_columns": ["VEICULO_CHASSI","VALOR_AGREGADO" ,"VALOR_PISCOFINS","VALOR_COMPRA" ,"VALOR_CUSTOCOMPRA",
                                   "VALOR_CUSTOOPERACIONAL"]
        }
        }
    ]),
    Bronze('nbs').add_tables([
        'andamento',
        'BAIXA_CONTAS_RECEBER',
        'baixa_adiantamento',
        'baixa_conta_pagar',
        'BQ_MOBILIDADE_ESTOQUE',
        'BQ_MOBILIDADE_OS',
        'ADIANTAMENTO',
        'cardex_contabil',
        'cartao_credito',
        'cidades',
        'centro_custo',
        'clientes',
        'cliente_diverso',
        'CLIENTES_FROTA',
        'conta_corrente',
        'CONTAS_RECEBER',
        'conta_contabil',
        'creceber_cartao',
        'creceber_cartao_parc',
        'concessionarias',
        'cores_externas',
        'cores_internas',
        'compra',
        'CUSTOS_ESPECIFICOS',
        'tbDeParaEmpresasLocais',
        'endereco_por_inscricao',
        'empresas',
        "empresas_departamentos",
        'empresas_usuarios',
        'exercicio_contabil',
        'ev_agendados',
        'exercicio_contabil_empresa',
        'garantia_renault_capa',
        'forma_cobranca',
        'forma_pgto',
        'FATURA_RECEBER',
        'fornecedor_estoque',
        'itens',
        'itens_custos',
        'itens_classe_contabil',
        'itens_fornecedor',
        'itens_grupo_interno',
        'ITENS_SUB_GRUPO',
        'interf_veic_entrada_holdback',
        'marcas',
        'operacoes',
        'os',
        'os_agenda',
        'os_dados_veiculos',
        'os_serv_orc',
        'os_servicos',
        'os_status',
        'os_tipos',
        'os_tipos_empresas',
        'os_tempos_executados',
        'os_requisicoes',
        'pat_cadastro',
        'pat_especie',
        'pat_situacao',
        'pat_depreciacao',
        'parm_sys2',
        'parm_sys3',
        'produtos',
        'produtos_modelos',
        'servicos_tecnicos',
        'servicos_setores',
        'setor_venda',
        'TIPO_ADIANTAMENTO',
        'totalizador_mensal',
        'venda_itens',
        'venda_os',
        'veic_kardex',
        'veic_situacao',
        'veiculos_custos_especificos',
        'VEIC_CUSTOS_VENDIDOS',
        'veiculos_custos',
        'veiculos',
        'venda_status',
        'VENDAS_CANCELAMENTO',
        'VENDA_CONTROLE_ENTREGA',
        'vw_conta_pagar',
        'SEGURADORA',
        'ORC_MAPA',
        'INTERMEDIADOR_ECOMMERCE',
        'GERENCIA_NFE',
        'NFE_FILA',
        'NFSE_MOVIMENTO',
        'CFE_MOVIMENTO',
        'CFE_FILA',
        'itens_historico',
        {
        "vw_endereco": {
            "times_per_day": 1
        },
        "pagamento_venda": {
                "times_per_day": 1
        },
        "grupo_pc": {
                "id_columns": ["cod_grupo_pc"]
        },
        'lancamento_contabil' : {
                "times_per_day": 1
        },
        "natureza_receita_despesa": {
                "id_columns": ["cod_natureza_receita_despesa", "cod_grupo_pc"]
        },
        "servicos": {
                "id_columns": ["cod_servico"],
                "date_columns": ["data_inclusao", "data_saf_alt", "data_saf_envio"]
        },
        "vendas": {
                "id_columns": ["cod_empresa", "controle", "serie"],
                "date_columns": ["emissao", "data_emissao_sistema", "data_envio_h3s_honda", "data_envio_myhonda", "data_faturamento", "data_fixsped", "data_hora_saida", "data_inclusao", "data_saf_alt", "data_saf_envio", "data_venda_detran", "emissao_nf_eletronica", "proposta_data_dev", "tranf_icms_mes_ano"],
                # "normal_columns": ["descontos_servicos", "grupo", "cod_proposta", "vendedor", "cod_cliente", "cod_modelo", "cod_operacao", "cod_produto", "cod_setor", "numero_os", "status", "total_servicos", "cod_grupo_pc", "cod_natureza", "total_descontos", "cod_empresa_divisao", "cod_empresa_departamento", "total_nota"]
        },
        "veiculos_propostas": {
                "id_columns": ["cod_proposta"],
                "date_columns": ["emissao", "data_aprovacao", "data_cancelamento", "data_devolucao", "data_envio_h3s_honda", "data_envio_myhonda", "data_envio_pedido_h3s_honda", "data_pedido_bndv", "data_previsao_emplacamento", "data_prevista", "data_reserva", "data_resposta_perfil", "data_ultima_atualizacao", "data_venda", "previsao_entrega", "validade"]
        }}
    ]),
    Bronze('syonet').add_tables([
        'syo_acao',
        'syo_campointerfacenegociacao',
        'syo_camposregistrointerface',
        'syo_cliente',
        'syo_clientenovaclassificacao',
        'syo_criterio',
        'syo_criterioclassificacao',
        'syo_criterionovaclassificacao',
        'syo_dadosinterfacecliente',
        'syo_donoconta',
        'syo_donocontacliente',
        'syo_empresa',
        'syo_empresacliente',
        'syo_etapafunil',
        'syo_evento',
        'syo_faixanovaclassificacao',
        'syo_historicoetapafunilevento',
        'syo_interfacenegociacao',
        'syo_motivoresultado',
        'syo_oficina',
        'syo_peca',
        'syo_registrointerface',
        'syo_tipoevento',
        'syo_usuario',
        'syo_veiculo',
        'tb_BD_BUDGET2',
        'tb_tiv'

    ]),
    Bronze('newcon').add_tables([
        'conaa002',
        'conaa003',
        'conaa008k',
        'conbe000',
        'conbe001',
        'conbe003',
        'conbe009',
        'conbe012',
        'conbe013',
        'conbe015a',
        'conbe023',
        'conbe025',
        'conbi003',
        'conbi010',
        'conbi800tmp',
        'concc000',
        'concc001',
        'concc025',
        'concc030',
        'concc030l',
        'concc036',
        'concc037a',
        'concc037b',
        'concp005',
        'concp006',
        'concp007',
        'concp009',
        'concp010',
        'concp012',
        'concp015',
        'concp016',
        'conco001',
        'conco001a',
        'conco003',
        'conco021a',
        'conco021i',
        'conct000',
        'confi001',
        'confi002',
        'confi005',
        'confi005c',
        'confi005e',
        'confi005f',
        'confi005h',
        'confi009',
        'confi016a',
        'confi016b',
        'confi016e',
        'confi030',
        'congr001',
        'congr001c',
        'congr001f',
        'congr001n',
        'congr011',
        'congr011a',
        'congr011b',
        'conve002',
        'conve002a',
        'conve002b',
        'conve002d',
        'conve002f',
        'conve002g',
        'conve003',
        'conve008',
        'conve016',
        'conve017',
        'conve018',
        'conve018b',
        'conve018c',
        'conve020',
        'conve024',
        'conve037',
        'conve046',
        'conve060',
        'conve064',
        'conve064e',
        'conve064m',
        'conve064t',
        'conve065',
        'conve074',
        'conve075',
        'conve076',
        'conve201',
        'corcc000',
        'corcc019',
        'corcc023',
        'corcc023a',
        'corcc024',
        'corcc026',
        'corcc027',
        'corcc030',
        'corcc036',
        'corcc048',
        'vw_confi005c_confi005',
        'vw_grproduto',

        {
        'conbe007': {
            "id_columns": ["id_bem"]
        },
        'confi006l' : {
            "id_columns": ["id_confi006c"]
        },
        'conve001': {
                "id_columns": ["id_ponto_venda"]
        },
        'conve009': {
                "id_columns": ["id_equipe_venda"],
                "date_columns": ["dh_inclusao","dh_alteracao"]
        },
        'conve014': {
                "id_columns": ["id_comissionado","id_pessoa"]
        },
        'conve038': {
            "id_columns": ["id_cota"]
        },
        'conve041': {
                "id_columns": ["id_plano_venda"]
        },
        'corcc015': {
            "id_columns": ["id_cidade"]
        },
        'fn_corcc000_conve002': {
            "query": """
                SELECT
                        CASE WHEN A.NO_PARCELA = (SELECT MAX(NO_Parcela) FROM dbo.fn_dsCoAvisoCobranca(CONVE002.ID_Cota, CORCC000.DT_Base_Processamento, CORCC000.DT_Base_Processamento)) 
                                THEN (SELECT VL_Diferenca FROM fn_dsVeVLSaldoDevedor(CONVE002.ID_Cota,  CORCC000.DT_Base_Processamento,  0))
                                ELSE 0 
                        END AS TITULO_SALDO,
                        CONVE002.ID_Cota,
                        CONVE002.ID_Comissionado,
                        CONVE002.ID_Pessoa,
                        CONVE002.ID_CONCC030,
                        CONVE002.ID_GRUPO,
                        CONVE002.ID_Plano_Venda,
                        CONVE002.Versao,
                        CONVE002.CD_COTA,
                        CONVE002.ID_Ponto_Venda,
                        a.id_assembleia,
                        a.VL_MU,
                        a.VL_JU,
                        a.VL_LANCAMENTO,
                        a.VL_MJ,
                        a.NO_PARCELA,
                        a.DT_Vencimento,
                        pa.VL_FC,
                        pa.VL_FR,
                        pa.VL_TA,
                        pa.VL_SG
                FROM CORCC000 
                CROSS JOIN CONVE002  
                CROSS APPLY dbo.fn_dsCoAvisoCobranca(CONVE002.ID_Cota, CORCC000.DT_Base_Processamento, CORCC000.DT_Base_Processamento) AS a  
                CROSS APPLY fn_dsAtValorParcela (CONVE002.ID_Cota,A.NO_PARCELA, CORCC000.DT_Base_Processamento) PA
                WHERE CONVE002.Versao = 0
                AND A.DT_Vencimento >= DATEADD(yy,-5,getdate())
                """,
             "times_per_day": 1
        },
        'pivot_confi005a': {
            "query": """
                select
                        id_movimento_grupo,
                        vl_fc   = isnull([FC],0),
                        vl_fr   = isnull([FR],0),
                        vl_ta   = isnull([TA],0),
                        vl_ad   = isnull([AD],0),
                        vl_sg   = isnull([SG],0),
                        vl_mj   = isnull([MJ],0),
                        vl_mu   = isnull([MU],0),
                        vl_ju   = isnull([JU],0),
                        vl_rp   = isnull([RP],0),
                        vl_cpmf = isnull([CP],0),
                        vl_sv   = isnull([SV],0),
                        vl_si   = isnull([SI],0),
                        vl_sq   = isnull([SQ],0),
                        vl_sc   = isnull([SC],0),
                        vl_sd   = isnull([SD],0),
                        vl_ou   = isnull([OU],0),
                        vl_td   = isnull([TD],0),
                        vl_tp   = isnull([TP],0)
                from
                        confi005a with (nolock)
                        pivot (sum(vl_lancamento) for id_tipo_contribuicao in ([FC], [FR], [TA], [AD], [SG], [MJ], [MU], [JU], [RP], [CP], [SV], [SI], [SQ], [SC], [SD], [OU], [TD], [TP])) as pvt
                """,
             "times_per_day": 1
        },
        'pivot_confi005f': {
            "query": """
                select
                        id_movimento_grupo,
                        pe_fc_ide = isnull([PN],0) + isnull([PA],0),
                        pe_fc_nor = isnull([PN],0),
                        pe_fc_ant = isnull([PA],0),
                        pe_fr = isnull([FR],0),
                        pe_ta = isnull([TA],0),
                        pe_ad = isnull([AD],0),
                        pe_td = isnull([TD],0),
                        pe_tp = isnull([TP],0),
                        pe_ou = isnull([OU],0),
                        pe_mj = isnull([MJ],0),
                        pe_cpmf = isnull([CP],0),
                        pe_mu = isnull([MU],0),
                        pe_ju = isnull([JU],0)
                from
                        confi005f with (nolock)
                        pivot (sum(pe_lancamento) for id_tipo_contribuicao in ([PN], [PA], [FR], [TA], [AD], [MJ], [RP], [CP], [TD], [TP], [OU], [MU], [JU] )) as pvt
                """,
             "times_per_day": 1
        },
        # "FN_DSVEVLSALDODEVEDOR": {
        #     "query": """
        #         SELECT
        #         c.id_cota,
        #         resultado.*
        #     FROM 
        #         CONVE002 c
        #     CROSS APPLY 
        #         FN_DSVEVLSALDODEVEDOR(
        #             c.id_cota, 
        #             GETDATE(), 
        #             dbNewCon.DBO.FN_VEVLBEMCOTA(c.id_cota, GETDATE(), 0)
        #         ) AS resultado
        #         """,
        #      "times_per_day": 1
        # }
        }
    ]),
    Bronze('quiver').add_tables([
        'tabela_clientender',
        'tabela_clientfones',
        'tabela_documentos',
        'tabela_docsparcscom',
        'tabela_docsparcsprem',
        'Tabela_DocsParcsRep',
        'Tabela_DocsItens',
        'tabela_docsrepasses',
        'tabela_gruposhierarq',
        'tabela_sinistros',
        'Tabela_SinistroPagto',
        'Tabela_SinistroCober',
        {
        "tabela_clientes": {
            "id_columns": ["Cliente"],
            "date_columns": ["Data_inclusao", "Data_alteracao"],
            "normal_columns": []
        },
        "tabela_divisoes": {
                "id_columns": ["Divisao"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_formasreccom": {
                "id_columns": ["forma_recebimento"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_meiospagto": {
                "id_columns": ["meio_pagto"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_niveishierarq": {
                "id_columns": ["Nivel"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_produtos": {
                "id_columns": ["Produto"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_ramos": {
                "id_columns": ["Ramo"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_seguradoras": {
                "id_columns": ["Seguradora"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_subtiposdoc": {
                "id_columns": ["sub_tipo"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_tiposdocemiss": {
                "id_columns": ["tipo_documento"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        },
        "tabela_tiposendereco": {
                "id_columns": ["tipo_endereco"],
                "date_columns": ["Data_inclusao", "Data_alteracao"],
                "normal_columns": []
        }
        }
    ]),
    Bronze('corpore').add_tables([
        'gccusto',
        'gcalend',
        'gcoligada',
        'gferiado',
        'gfilial',
        'pfrateiofixo',
        'pfuncao',
        'psubstchefe',
        {
        "pfunc": {
                #"id_columns": ["codigo"],
                #"date_columns": ["reccreatedon", "recmodifiedon"],
                "normal_columns": ["codcoligada", "codfilial","chapa","codsituacao","codtipo","codsecao","codfuncao","antigasecao","codpessoa","dataadmissao","datademissao","reccreatedon","recmodifiedon"]
        },
        "ppessoa": {
                "id_columns": ["codigo"],
                "date_columns": ["reccreatedon", "recmodifiedon"],
                "normal_columns": ["nome", "email", "cpf"]
        },
        "psecao": {
            #"id_columns": ["codigo"],
            #"date_columns": ["reccreatedon", "recmodifiedon"],
            "normal_columns": ["codcoligada", "codigo","descricao","cgc","secaodesativada","codfilial","codigopai","reccreatedon","recmodifiedon"]
        }
        }
    ]),
    Bronze('koneq').add_tables([
        'device_consumption_average',
        'equipments',
        'model_family',
        'models',
        'regionals',
        'tlm_devices'
    ]),
    Bronze('dw_bamaq').add_tables([
        # 'vendas',
        'ponto_vendas',
        'subcanal_venda_ponto_vendas',
        'subcanal_venda',
        {
            'vendas': {
                "times_per_day": 4
            }
        }
    ]),
    Bronze('sync').add_tables([
        {
        "bqvw_todoschamados": {
            "times_per_day": 5
        },
        "atividadedochamado": {
            "times_per_day": 5
        },
        "statusdaatividadedochamado": {
            "times_per_day": 5
        },
        "tipodestatusdeatividade": {
            "times_per_day": 5
        }
        }
    ]),
    Bronze('space').add_tables([
        "tbPosicaoEstoqueNew"
    ]),
    Bronze('netadmin_aud').add_tables([
        "office365license",
        "office365user"
    ]),
]

with DAG(
    'V2-BRONZE',
    default_args=default_args,
    description='DAG para ETL Bronze de dados da BAMAQ',
    schedule_interval=schedule_interval,
    max_active_runs=1,
    max_active_tasks=17, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, instances, triggers=['V2-FREEZE'])

    start_etl_task >> end_etl_task >> dag_stats