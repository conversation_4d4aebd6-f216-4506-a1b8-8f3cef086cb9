from airflow import DAG
from airflow.utils.dates import days_ago
from Auxiliares_ETL_v2.models import *
from Auxiliares_ETL_v2.functions_custom import *

### IMPORTANTE ###
#
# Aperte CTRL + K e CTRL + 0 para navegar no script
#

first_instances = [
    # Silver Dim
    Silver('dim_nbs_os')
    .query(
        Query.Bronze_to_silver.silver_dim_nbs_os
    ),
    Silver('dim_nbs_veiculos')
    .query(
        Query.Bronze_to_silver.silver_dim_nbs_veiculos
    ),
    # Silver('dim_nbs_vendas')
    # .query(
    #     Query.Bronze_to_silver.silver_dim_nbs_vendas
    # ),

    # Syonet
    Silver('crm_auto_bicrmautomotivo').sys('syonet')
    .query(
        Query.Direct_Queries.crm_auto_bicrmautomotivo
        ),
    Silver('crm_equipe').sys('syonet')
    .query(
        Query.Direct_Queries.crm_equipe
        ),
    Silver('crm_auto').sys('syonet')
    .query(
        Query.Direct_Queries.crm_auto
        ),
    Silver('crm_auto_testdrive').sys('syonet').times_per_day(5)
    .query(
        Query.Direct_Queries.crm_auto_testdrive
        ),
    Silver('crm_auto_historico_etapas').sys('syonet')
    .query(
        Query.Direct_Queries.crm_auto_historico_etapas
        ),
    Silver('crm_auto_historico_acao').sys('syonet')
    .query(
        Query.Direct_Queries.crm_auto_historico_acao
        ),
    Silver('crm_auto_faturamento').sys('syonet')
    .query(
        Query.Direct_Queries.crm_auto_faturamento
        ),
    
    # Quiver
    Silver('faturamento').sys('quiver').times_per_day(1)
    .query(
        Query.Direct_Queries.quiver_faturamento
    ),
    Silver('clientes').sys('quiver').times_per_day(1)
    .query(
        Query.Direct_Queries.quiver_clientes
    ),
    Silver('financeiro').sys('quiver').times_per_day(1)
    .query(
        Query.Direct_Queries.quiver_financeiro
    ),
    
    # Nbs
    Silver('formapagto_faturamento').sys('nbs').times_per_day(1)
    .query(
        Query.Direct_Queries.nbs_formapagto_faturamento
    ),
    Silver('faturamento').sys('nbs').times_per_day(1)
    .query(
        Query.Direct_Queries.nbs_faturamento
    ),
    Silver('clientes').sys('nbs').times_per_day(5)
    .query(
        Query.Direct_Queries.nbs_clientes
    ),
    Silver('financeiro').sys('nbs').times_per_day(1)
    .query(
        Query.Direct_Queries.nbs_financeiro
    ),
    Silver('posvendas').sys('nbs')
    .query(
        Query.Direct_Queries.nbs_posvendas
    ),
    Silver('fat_pecas').sys('nbs')
    .query(
        Query.Direct_Queries.nbs_fat_pecas
    ),
    Silver('fat_posvendas').sys('nbs')
    .query(
        Query.Direct_Queries.nbs_fat_posvendas
    ),
    Silver('fat_posvendas_pecas_descontos').sys('nbs')
    .query(
        Query.Direct_Queries.nbs_fat_posvendas_pecas_descontos
    ),    
    # Dealer
    Silver('formapagto_faturamento').sys('dealer').times_per_day(1)
    .query(
        Query.Direct_Queries.dealer_formapagto_faturamento
    ),
    Silver('faturamento').sys('dealer').times_per_day(1)
    .query(
        Query.Direct_Queries.dealer_faturamento
    ),
    Silver('clientes').sys('dealer').times_per_day(1)
    .query(
        Query.Direct_Queries.dealer_clientes
    ),
    Silver('financeiro').sys('dealer').times_per_day(1)
    .query(
        Query.Direct_Queries.dealer_financeiro
    ),
    Silver('seminovos').sys('dealer')
    .query(
        Query.Direct_Queries.dealer_seminovos
    ),
    Silver('fiscal_iss_retido').sys('dealer')
    .query(
        Query.Direct_Queries.dealer_fiscal_iss_retido
    ),
    Silver('contabil_iss_retido').sys('dealer')
    .query(
        Query.Direct_Queries.dealer_contabil_iss_retido
    ),
    
    # Newcon
    Silver('formapagto_faturamento').sys('newcon').times_per_day(1)
    .query(
        Query.Direct_Queries.newcon_formapagto_faturamento
    ),
    Silver('faturamento').sys('newcon').times_per_day(1)
    .query(
        Query.Direct_Queries.newcon_faturamento
    ),
    Silver('clientes').sys('newcon').times_per_day(1)
    .query(
        Query.Direct_Queries.newcon_clientes
    ),
    # Silver('financeiro').sys('newcon').times_per_day(1)
    # .query(
    #     Query.Direct_Queries.newcon_financeiro
    # ),
    
    Silver('indicacao_propostas').sys('newcon')
    .query(
        Query.Direct_Queries.newcon_indicacao_propostas
    ),
    Silver('indicacao_vendas').sys('newcon')
    .query(
        Query.Direct_Queries.newcon_indicacao_vendas
    ),
    # Silver('cotas').sys('newcon')
    # .query(
    #     Query.Direct_Queries.newcon_cotas
    # ),
    # Silver('grupos').sys('newcon')
    # .query(
    #     Query.Direct_Queries.newcon_grupos
    # ),
    
    Silver('propostas').sys('newcon')
    .query(
        Query.Direct_Queries.newcon_propostas
    ),
    Silver('contas_a_receber').sys('newcon').index({
        "idx_silver_newcon_contas_a_receber_idcota": ["id_cota"]
    }).query(
        Query.Direct_Queries.newcon_contas_a_receber
    ),
    
    # Corpore
    Silver('custo_folha').sys('corpore')
    .query(
        Query.Direct_Queries.copore_custo_folha
    ),
     # Corpore
    
    # Sge
    Silver('bandeirasgrupobamaq').sys('sge')
    .query(
        Query.Direct_Queries.sge_bandeirasgrupobamaq
    ),
    
    # Dw corporativo
    Silver('dealer_cpc47')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_cpc47
    ),
    Silver('dealer_compra_maquinas')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_compra_maquinas
    ),
    Silver('dealer_vendas')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_vendas
    ),
    Silver('dealer_valor_os')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_valor_os
    ),
    Silver('dealer_valor_ostipoos')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_valor_ostipoos
    ),
    Silver('dealer_fat_maquinas')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_fat_maquinas
    ),
    Silver('dealer_fat_pecas')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_fat_pecas
    ),
    Silver('dealer_fat_tipoos')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_fat_tipoos
    ),
    Silver('dealer_os')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_os
    ),
    Silver('dealer_estoque_maquinas')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_estoque_maquinas
    ),
    Silver('dealer_fat_produtivo')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_fat_produtivo
    ),
    Silver('dealer_veiculos_dadoscompl')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_veiculos_dadoscompl
    ),
    Silver('newcon_financeiro').times_per_day(1)
    .query(
        Query.Bronze_to_silver.select_silver_newcon_financeiro
    ),
    Silver('newcon_extratofinanceiro').index({
        "idx_silver_newcon_extratofinanceiro_idcota": ["id_cota"]
    }).query(
        Query.Bronze_to_silver.select_silver_newcon_extratofinanceiro
    ),
    Silver('newcon_parametrizacao_regracomissao').times_per_day(5).query(
        Query.Bronze_to_silver.select_silver_newcon_parametrizacao_regracomissao
    ),
    # Silver('newcon_regracomissao_coberta_planovenda').times_per_day(5).query(
    #     Query.Bronze_to_silver.select_silver_newcon_regracomissao_coberta_planovenda
    # ),
    
    Silver('newcon_vendas').query(
        Query.Bronze_to_silver.select_silver_newcon_vendas
    ),
    Silver('newcon_resultado_assembleia').query(
        Query.Bronze_to_silver.select_silver_newcon_resultado_assembleia
    ),
    Silver('newcon_grupos').query(
        Query.Bronze_to_silver.select_silver_newcon_grupos
    ),
    Silver('newcon_ponto_venda').query(
        Query.Bronze_to_silver.select_silver_newcon_ponto_venda
    ),
    Silver('newcon_equipe_venda').query(
        Query.Bronze_to_silver.select_silver_newcon_equipe_venda
    ),
    Silver('newcon_comissionado').query(
        Query.Bronze_to_silver.select_silver_newcon_comissionado
    ),
    Silver('newcon_pessoas').query(
        Query.Bronze_to_silver.select_silver_newcon_pessoas
    ),
    Silver('dw_bamaq_consorcio')
    .query(
        Query.Bronze_to_silver.select_silver_dw_bamaq_consorcio
    ),
    Silver('quiver_seguros')
    .query(
        Query.Bronze_to_silver.select_silver_quiver_seguros
    ),
    Silver('quiver_cgf')
    .query(
        Query.Bronze_to_silver.select_silver_quiver_cgf
    ),
    Silver('consorcio_vendas_indicaco').sys('dw_bamaq')
    .query(Query.Direct_Queries.consorcio_vendas_indicacoes
    ),
    Silver('syo_vendas_seg')
    .query(
        Query.Bronze_to_silver.select_silver_syo_vendas_seg
    ),
    Silver('syo_cgf')
    .query(
        Query.Bronze_to_silver.select_silver_syo_cgf
    ), 
    Silver('nbs_capex')
    .query(
        Query.Bronze_to_silver.select_silver_nbs_capex
    ),
    Silver('nbs_vendas')
    .query(
        Query.Bronze_to_silver.select_silver_nbs_vendas
    ),
    Silver('nbs_controladoria_veiculos')
    .query(
        Query.Bronze_to_silver.select_silver_nbs_controladoria_veiculos
    ),
    Silver('nbs_controladoria_servicos')
    .query(
        Query.Bronze_to_silver.select_silver_nbs_controladoria_servicos
    ),
    Silver('dealer_titulos')
    .query(
        Query.Bronze_to_silver.select_silver_dealer_titulos
    ),
    Silver('centrocustocolaborador')
    .query(
        Query.Bronze_to_silver.select_silver_corpore_centrocustocolaborador
    ),
    Silver('empresaferiado')
    .query(
        Query.Bronze_to_silver.select_silver_corpore_empresaferiado
    ),
    Silver('autom_heavy_marketshare').times_per_day(5)
    .query(
        Query.Bronze_to_silver.select_silver_autom_heavy_marketshare
    ),
    Silver('dealer_pedidos_peca').query(
        Query.Bronze_to_silver.select_silver_dealer_pedidos_peca
    ),
    Silver('nbs_seminovos').query(
        Query.Bronze_to_silver.select_silver_nbs_seminovos
    )
]

second_instances = [
    
    #DwCorporativo
    Silver('newcon_histogramacota').index({
        "idx_silver_newcon_histogramacota_idcota": ["id_cota"]
    }).query(
        Query.Bronze_to_silver.select_silver_newcon_histogramacota
    ),
    Silver('newcon_lances_credenciados').query(
        Query.Bronze_to_silver.select_silver_newcon_lances_credenciados
    ),
    Silver('newcon_lances_credenciados_medias').query(
        Query.Bronze_to_silver.select_silver_newcon_lances_credenciados_medias
    )

]

with DAG(
    'V2-SILVER',
    default_args=default_args,
    description='DAG para ETL Bronze de dados da BAMAQ',
    schedule_interval=None,
    max_active_runs=1,
    max_active_tasks=12, 
    start_date=days_ago(1),
    catchup=False
) as dag:

    start_etl_task, end_etl_task, dag_stats = create_etl_tasks(dag, [first_instances, second_instances], triggers=['V2-GOLD'])

    start_etl_task >> end_etl_task >> dag_stats